<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SSE协议测试</title>
    <style>
        body {
            font-family: 'Consolas', 'Monaco', monospace;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #0d1117;
            color: #c9d1d9;
            line-height: 1.6;
        }
        .container {
            background: #161b22;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #30363d;
        }
        h1, h2 {
            color: #58a6ff;
            margin-top: 0;
        }
        button {
            padding: 8px 16px;
            margin: 5px;
            border: 1px solid #30363d;
            border-radius: 6px;
            background: #21262d;
            color: #c9d1d9;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s;
        }
        button:hover {
            background: #30363d;
            border-color: #58a6ff;
        }
        button:active {
            background: #0969da;
            transform: scale(0.98);
        }
        .log-area {
            background: #0d1117;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Consolas', monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #30363d;
            margin-top: 10px;
        }
        .success { color: #3fb950; }
        .error { color: #f85149; }
        .info { color: #58a6ff; }
        .warning { color: #d29922; }
        .event { color: #bc8cff; }
        input[type="text"] {
            width: 300px;
            padding: 8px;
            border: 1px solid #30363d;
            border-radius: 6px;
            background: #0d1117;
            color: #c9d1d9;
            font-size: 14px;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-connected { background: #3fb950; }
        .status-disconnected { background: #f85149; }
        .status-connecting { background: #d29922; }
    </style>
</head>
<body>
    <h1>🌊 SSE (Server-Sent Events) 协议测试</h1>
    
    <div class="container">
        <h2>📡 连接状态</h2>
        <p>
            <span id="statusIndicator" class="status-indicator status-disconnected"></span>
            <span id="statusText">未连接</span>
        </p>
        <p><strong>后端地址:</strong> <span id="backendUrl">http://localhost:8000</span></p>
    </div>

    <div class="container">
        <h2>🧪 SSE测试</h2>
        <div>
            <input type="text" id="messageInput" placeholder="输入测试消息..." value="1+1=?">
            <button onclick="testSSE()">🚀 发送SSE请求</button>
            <button onclick="testStandardSSE()">📡 标准SSE连接</button>
            <button onclick="clearLog()">🧹 清空日志</button>
        </div>
        
        <div id="sseLog" class="log-area"></div>
    </div>

    <div class="container">
        <h2>🔍 SSE协议分析</h2>
        <button onclick="analyzeSSEFormat()">📊 分析SSE格式</button>
        <button onclick="testEventTypes()">🎯 测试事件类型</button>
        <button onclick="testReconnection()">🔄 测试重连机制</button>
        
        <div id="analysisLog" class="log-area"></div>
    </div>

    <div class="container">
        <h2>📋 SSE协议规范</h2>
        <pre style="color: #7d8590; font-size: 12px;">
SSE (Server-Sent Events) 协议格式:

event: &lt;事件类型&gt;
id: &lt;消息ID&gt;
data: &lt;JSON数据&gt;

&lt;空行表示事件结束&gt;

标准事件类型:
- connected: 连接建立
- start: 开始处理
- chunk: 内容块
- complete: 处理完成
- end: 流结束
- error: 错误事件
        </pre>
    </div>

    <script>
        let eventSource = null;
        let messageCount = 0;

        function log(message, type = 'info', targetId = 'sseLog') {
            const logArea = document.getElementById(targetId);
            const timestamp = new Date().toLocaleTimeString();
            const className = type;
            logArea.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            logArea.scrollTop = logArea.scrollHeight;
        }

        function updateStatus(status, text) {
            const indicator = document.getElementById('statusIndicator');
            const statusText = document.getElementById('statusText');
            
            indicator.className = `status-indicator status-${status}`;
            statusText.textContent = text;
        }

        function clearLog() {
            document.getElementById('sseLog').innerHTML = '';
            document.getElementById('analysisLog').innerHTML = '';
        }

        async function testSSE() {
            const message = document.getElementById('messageInput').value;
            if (!message.trim()) {
                log('❌ 请输入测试消息', 'error');
                return;
            }

            log(`🚀 开始SSE测试: "${message}"`, 'info');
            updateStatus('connecting', '连接中...');

            try {
                const response = await fetch('http://localhost:8000/chat/stream', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'text/event-stream',
                    },
                    body: JSON.stringify({ message }),
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                updateStatus('connected', '已连接');
                log('✅ SSE连接建立成功', 'success');

                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let buffer = '';
                let eventCount = 0;

                while (true) {
                    const { done, value } = await reader.read();
                    
                    if (done) {
                        log('📥 SSE流读取完成', 'info');
                        break;
                    }

                    buffer += decoder.decode(value, { stream: true });
                    const lines = buffer.split('\n');
                    buffer = lines.pop() || '';

                    let currentEvent = { type: '', id: '', data: '' };

                    for (const line of lines) {
                        log(`📝 原始行: "${line}"`, 'info');

                        if (line === '') {
                            // 空行表示事件结束
                            if (currentEvent.data) {
                                eventCount++;
                                log(`🎯 事件 #${eventCount}: type="${currentEvent.type}", id="${currentEvent.id}", data="${currentEvent.data}"`, 'event');
                                
                                // 处理事件
                                handleSSEEvent(currentEvent);
                                currentEvent = { type: '', id: '', data: '' };
                            }
                        } else if (line.startsWith('event: ')) {
                            currentEvent.type = line.slice(7);
                        } else if (line.startsWith('id: ')) {
                            currentEvent.id = line.slice(4);
                        } else if (line.startsWith('data: ')) {
                            currentEvent.data = line.slice(6);
                        }
                    }
                }

                updateStatus('disconnected', '已断开');
                log(`✅ SSE测试完成，共处理 ${eventCount} 个事件`, 'success');

            } catch (error) {
                updateStatus('disconnected', '连接失败');
                log(`❌ SSE测试失败: ${error.message}`, 'error');
            }
        }

        function handleSSEEvent(event) {
            try {
                if (event.data === '[DONE]') {
                    log('🏁 收到结束标记', 'success');
                    return;
                }

                const data = JSON.parse(event.data);
                
                switch (event.type) {
                    case 'connected':
                        log(`🔗 ${data.message}`, 'success');
                        break;
                    case 'start':
                        log(`🚀 ${data.message}`, 'info');
                        break;
                    case 'chunk':
                        log(`📝 内容块 #${data.chunk_id}: "${data.content}"`, 'success');
                        break;
                    case 'complete':
                        log(`✅ 完成，共 ${data.total_chunks} 个块`, 'success');
                        break;
                    case 'end':
                        log('🏁 流结束', 'info');
                        break;
                    case 'error':
                        log(`❌ 错误: ${data.error}`, 'error');
                        break;
                    default:
                        log(`ℹ️ 未知事件类型: ${event.type}`, 'warning');
                }
            } catch (parseError) {
                log(`⚠️ 解析事件数据失败: ${parseError.message}`, 'warning');
            }
        }

        function testStandardSSE() {
            log('📡 测试标准EventSource连接...', 'info');
            
            // 注意：EventSource只支持GET请求，这里仅用于测试连接
            try {
                eventSource = new EventSource('http://localhost:8000/health');
                
                eventSource.onopen = function(event) {
                    log('✅ EventSource连接已打开', 'success');
                    updateStatus('connected', 'EventSource已连接');
                };
                
                eventSource.onmessage = function(event) {
                    log(`📨 收到消息: ${event.data}`, 'info');
                };
                
                eventSource.onerror = function(event) {
                    log('❌ EventSource连接错误', 'error');
                    updateStatus('disconnected', 'EventSource错误');
                };
                
                // 5秒后关闭连接
                setTimeout(() => {
                    if (eventSource) {
                        eventSource.close();
                        log('🔌 EventSource连接已关闭', 'info');
                        updateStatus('disconnected', '已断开');
                    }
                }, 5000);
                
            } catch (error) {
                log(`❌ EventSource测试失败: ${error.message}`, 'error');
            }
        }

        function analyzeSSEFormat() {
            log('📊 分析SSE协议格式...', 'info', 'analysisLog');
            
            const sseExample = `event: chunk
id: 123
data: {"content": "Hello", "status": "streaming"}

event: end
id: 124
data: [DONE]

`;
            
            log('📋 标准SSE格式示例:', 'info', 'analysisLog');
            log(sseExample, 'info', 'analysisLog');
            
            log('✅ 格式分析完成', 'success', 'analysisLog');
        }

        function testEventTypes() {
            log('🎯 测试SSE事件类型处理...', 'info', 'analysisLog');
            
            const eventTypes = ['connected', 'start', 'chunk', 'complete', 'end', 'error'];
            
            eventTypes.forEach((type, index) => {
                setTimeout(() => {
                    log(`📝 测试事件类型: ${type}`, 'event', 'analysisLog');
                }, index * 500);
            });
            
            setTimeout(() => {
                log('✅ 事件类型测试完成', 'success', 'analysisLog');
            }, eventTypes.length * 500);
        }

        function testReconnection() {
            log('🔄 测试SSE重连机制...', 'info', 'analysisLog');
            log('💡 重连机制说明:', 'info', 'analysisLog');
            log('  - 自动重连间隔: 3秒', 'info', 'analysisLog');
            log('  - 最大重连次数: 5次', 'info', 'analysisLog');
            log('  - 使用Last-Event-ID恢复', 'info', 'analysisLog');
            log('✅ 重连机制说明完成', 'success', 'analysisLog');
        }

        // 页面加载时初始化
        window.onload = function() {
            log('🚀 SSE协议测试工具已加载', 'success');
            log('💡 请确保后端服务已启动 (http://localhost:8000)', 'info');
            
            // 测试后端连接
            fetch('http://localhost:8000/health')
                .then(response => {
                    if (response.ok) {
                        log('✅ 后端服务连接正常', 'success');
                    } else {
                        log('⚠️ 后端服务响应异常', 'warning');
                    }
                })
                .catch(error => {
                    log('❌ 无法连接到后端服务', 'error');
                });
        };
    </script>
</body>
</html>
