import React, { useState, useRef, useEffect } from 'react'
import { Card, Input, Button, Space, Typography, message } from 'antd'
import { motion, AnimatePresence } from 'framer-motion'
import { Send, Bot, User, Loader2 } from 'lucide-react'
import { chatService } from '../services/chatService'
import StreamingText from './StreamingText'

const { TextArea } = Input
const { Text } = Typography

interface Message {
  id: string
  content: string
  role: 'user' | 'assistant'
  timestamp: Date
  isStreaming?: boolean
}

const ChatInterface: React.FC = () => {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      content: '👋 你好！我是基于AutoGen的AI助手。我可以帮你解答问题、创作内容、编程协助等。请告诉我你需要什么帮助？',
      role: 'assistant',
      timestamp: new Date(),
    }
  ])
  const [inputValue, setInputValue] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [streamingMessageId, setStreamingMessageId] = useState<string | null>(null)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<any>(null)

  // 自动滚动到底部
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  // 发送消息
  const handleSendMessage = async () => {
    if (!inputValue.trim() || isLoading) return

    const userMessage: Message = {
      id: Date.now().toString(),
      content: inputValue.trim(),
      role: 'user',
      timestamp: new Date(),
    }

    // 添加用户消息
    setMessages(prev => [...prev, userMessage])
    const messageContent = inputValue.trim()
    setInputValue('')
    setIsLoading(true)

    // 创建助手消息占位符
    const assistantMessageId = (Date.now() + 1).toString()
    const assistantMessage: Message = {
      id: assistantMessageId,
      content: '',
      role: 'assistant',
      timestamp: new Date(),
      isStreaming: true,
    }

    setMessages(prev => [...prev, assistantMessage])
    setStreamingMessageId(assistantMessageId)

    console.log('🚀 发送消息:', messageContent)

    try {
      // 使用流式API
      await chatService.sendStreamMessage(
        messageContent,
        (chunk: string) => {
          console.log('📝 收到chunk:', chunk)
          // 更新流式消息内容
          setMessages(prev =>
            prev.map(msg =>
              msg.id === assistantMessageId
                ? { ...msg, content: msg.content + chunk }
                : msg
            )
          )
        },
        () => {
          console.log('✅ 流式完成')
          // 流式完成
          setMessages(prev =>
            prev.map(msg =>
              msg.id === assistantMessageId
                ? { ...msg, isStreaming: false }
                : msg
            )
          )
          setStreamingMessageId(null)
          setIsLoading(false)
        },
        (error: string) => {
          console.error('❌ 流式错误:', error)
          // 错误处理
          setMessages(prev =>
            prev.map(msg =>
              msg.id === assistantMessageId
                ? { ...msg, content: '抱歉，发生了错误。请重试。', isStreaming: false }
                : msg
            )
          )
          setStreamingMessageId(null)
          setIsLoading(false)
          message.error('发送消息失败，请重试')
        }
      )
    } catch (error) {
      console.error('💥 发送消息错误:', error)
      setMessages(prev => prev.slice(0, -1)) // 移除失败的消息
      setIsLoading(false)
      setStreamingMessageId(null)
      message.error('发送消息失败，请重试')
    }
  }

  // 处理键盘事件
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  // 清空聊天
  const handleClearChat = () => {
    setMessages([
      {
        id: '1',
        content: '👋 你好！我是基于AutoGen的AI助手。我可以帮你解答问题、创作内容、编程协助等。请告诉我你需要什么帮助？',
        role: 'assistant',
        timestamp: new Date(),
      }
    ])
    message.success('聊天记录已清空')
  }

  return (
    <div className="h-[calc(100vh-64px)] flex flex-col max-w-4xl mx-auto p-6">
      {/* 聊天消息区域 */}
      <Card className="flex-1 mb-6 glass-effect border-slate-600/30">
        <div className="h-full flex flex-col">
          <div className="flex-1 overflow-y-auto pr-2 space-y-4">
            <AnimatePresence>
              {messages.map((message, index) => (
                <motion.div
                  key={message.id}
                  initial={{ opacity: 0, y: 20, scale: 0.95 }}
                  animate={{ opacity: 1, y: 0, scale: 1 }}
                  exit={{ opacity: 0, y: -20, scale: 0.95 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                  className={`flex gap-3 ${message.role === 'user' ? 'flex-row-reverse' : 'flex-row'}`}
                >
                  {/* 头像 */}
                  <motion.div
                    whileHover={{ scale: 1.1 }}
                    className={`w-10 h-10 rounded-full flex items-center justify-center flex-shrink-0 ${
                      message.role === 'user' 
                        ? 'bg-gradient-to-r from-blue-500 to-purple-600' 
                        : 'bg-gradient-to-r from-emerald-500 to-cyan-600'
                    }`}
                  >
                    {message.role === 'user' ? (
                      <User className="w-5 h-5 text-white" />
                    ) : (
                      <Bot className="w-5 h-5 text-white" />
                    )}
                  </motion.div>

                  {/* 消息内容 */}
                  <div className={`flex flex-col ${message.role === 'user' ? 'items-end' : 'items-start'} max-w-[80%]`}>
                    <motion.div
                      whileHover={{ scale: 1.02 }}
                      className={`message-bubble ${message.role} relative`}
                    >
                      {message.role === 'assistant' && message.isStreaming ? (
                        <div className="text-slate-100">
                          <StreamingText
                            content={message.content}
                            isStreaming={true}
                            speed={50}
                            className="text-slate-100"
                          />
                        </div>
                      ) : (
                        <Text className={message.role === 'user' ? 'text-white' : 'text-slate-100'}>
                          {message.content}
                        </Text>
                      )}
                      
                      {/* 流式指示器 */}
                      {message.isStreaming && (
                        <motion.div
                          animate={{ opacity: [0.5, 1, 0.5] }}
                          transition={{ duration: 1.5, repeat: Infinity }}
                          className="inline-flex items-center ml-2"
                        >
                          <Loader2 className="w-3 h-3 animate-spin text-blue-400" />
                        </motion.div>
                      )}
                    </motion.div>
                    
                    {/* 时间戳 */}
                    <Text type="secondary" className="text-xs mt-1 px-2">
                      {message.timestamp.toLocaleTimeString('zh-CN', { 
                        hour: '2-digit', 
                        minute: '2-digit' 
                      })}
                    </Text>
                  </div>
                </motion.div>
              ))}
            </AnimatePresence>
            
            {/* 加载指示器 */}
            {isLoading && !streamingMessageId && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="flex items-center gap-3"
              >
                <div className="w-10 h-10 rounded-full bg-gradient-to-r from-emerald-500 to-cyan-600 flex items-center justify-center">
                  <Bot className="w-5 h-5 text-white" />
                </div>
                <div className="glass-effect rounded-2xl px-4 py-3">
                  <div className="loading-dots">
                    <span></span>
                    <span></span>
                    <span></span>
                  </div>
                </div>
              </motion.div>
            )}
            
            <div ref={messagesEndRef} />
          </div>
        </div>
      </Card>

      {/* 输入区域 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <Card className="glass-effect border-slate-600/30">
          <Space.Compact className="w-full">
            <TextArea
              ref={inputRef}
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="输入你的消息... (Enter 发送，Shift + Enter 换行)"
              autoSize={{ minRows: 1, maxRows: 4 }}
              className="chat-input flex-1"
              disabled={isLoading}
            />
            <Button
              type="primary"
              icon={<Send className="w-4 h-4" />}
              onClick={handleSendMessage}
              loading={isLoading}
              disabled={!inputValue.trim() || isLoading}
              className="send-button h-auto"
            />
          </Space.Compact>
          
          <div className="flex justify-between items-center mt-3 text-xs text-slate-400">
            <span>{inputValue.length}/2000</span>
            <Space>
              <Button 
                type="link" 
                size="small" 
                onClick={handleClearChat}
                className="!text-slate-400 hover:!text-white"
              >
                清空聊天
              </Button>
            </Space>
          </div>
        </Card>
      </motion.div>
    </div>
  )
}

export default ChatInterface
