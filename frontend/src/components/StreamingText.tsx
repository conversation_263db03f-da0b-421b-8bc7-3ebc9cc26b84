import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'

interface StreamingTextProps {
  content: string
  isStreaming?: boolean
  speed?: number
  className?: string
}

const StreamingText: React.FC<StreamingTextProps> = ({ 
  content, 
  isStreaming = false, 
  speed = 30,
  className = ''
}) => {
  const [displayedContent, setDisplayedContent] = useState('')
  const [currentIndex, setCurrentIndex] = useState(0)

  useEffect(() => {
    if (!isStreaming) {
      // 如果不是流式模式，直接显示全部内容
      setDisplayedContent(content)
      return
    }

    // 流式模式：逐字符显示
    if (currentIndex < content.length) {
      const timer = setTimeout(() => {
        setDisplayedContent(content.slice(0, currentIndex + 1))
        setCurrentIndex(currentIndex + 1)
      }, speed)

      return () => clearTimeout(timer)
    }
  }, [content, currentIndex, isStreaming, speed])

  useEffect(() => {
    // 当内容变化时重置索引
    if (isStreaming) {
      setCurrentIndex(0)
      setDisplayedContent('')
    } else {
      setDisplayedContent(content)
    }
  }, [content, isStreaming])

  return (
    <span className={className}>
      {displayedContent}
      {isStreaming && currentIndex < content.length && (
        <motion.span
          animate={{ opacity: [0, 1, 0] }}
          transition={{ duration: 1, repeat: Infinity }}
          className="inline-block w-0.5 h-4 bg-blue-400 ml-1"
        />
      )}
    </span>
  )
}

export default StreamingText
