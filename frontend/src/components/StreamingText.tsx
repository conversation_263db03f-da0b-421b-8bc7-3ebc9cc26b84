import React, { useState, useEffect, useRef } from 'react'
import { motion } from 'framer-motion'

interface StreamingTextProps {
  content: string
  isStreaming?: boolean
  speed?: number
  className?: string
}

const StreamingText: React.FC<StreamingTextProps> = ({
  content,
  isStreaming = false,
  speed = 20, // 更快的默认速度，类似DeepSeek
  className = ''
}) => {
  const [displayedContent, setDisplayedContent] = useState('')
  const [showCursor, setShowCursor] = useState(true)
  const contentRef = useRef('')
  const indexRef = useRef(0)
  const timerRef = useRef<NodeJS.Timeout | null>(null)

  // 清理Markdown格式的函数
  const cleanContent = (text: string): string => {
    return text
      .replace(/\*\*(.*?)\*\*/g, '$1') // 移除粗体标记 **text**
      .replace(/\*(.*?)\*/g, '$1')     // 移除斜体标记 *text*
      .replace(/`(.*?)`/g, '$1')       // 移除行内代码标记 `code`
      .replace(/#{1,6}\s/g, '')        // 移除标题标记 # ## ###
      .replace(/^\s*[-*+]\s/gm, '• ') // 将列表标记转换为圆点
      .replace(/^\s*\d+\.\s/gm, '')   // 移除有序列表数字
  }

  // DeepSeek风格的流式显示效果
  useEffect(() => {
    if (!isStreaming) {
      // 非流式模式，直接显示清理后的内容
      const cleaned = cleanContent(content)
      setDisplayedContent(cleaned)
      setShowCursor(false)
      return
    }

    // 流式模式
    const cleaned = cleanContent(content)
    contentRef.current = cleaned

    // 如果内容变化，重新开始显示
    if (displayedContent.length > cleaned.length) {
      indexRef.current = 0
      setDisplayedContent('')
    }

    // 逐字符显示，类似DeepSeek的效果
    const displayNextChar = () => {
      if (indexRef.current < contentRef.current.length) {
        const nextChar = contentRef.current[indexRef.current]
        setDisplayedContent(prev => prev + nextChar)
        indexRef.current++

        // 根据字符类型调整速度
        let delay = speed
        if (nextChar === '。' || nextChar === '！' || nextChar === '？') {
          delay = speed * 3 // 句号后稍微停顿
        } else if (nextChar === '，' || nextChar === '；') {
          delay = speed * 2 // 逗号后短暂停顿
        } else if (nextChar === ' ') {
          delay = speed * 0.5 // 空格快速跳过
        }

        timerRef.current = setTimeout(displayNextChar, delay)
      } else {
        // 显示完成，隐藏光标
        setShowCursor(false)
      }
    }

    // 开始显示
    if (indexRef.current < contentRef.current.length) {
      setShowCursor(true)
      timerRef.current = setTimeout(displayNextChar, speed)
    }

    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current)
      }
    }
  }, [content, isStreaming, speed])

  // 清理定时器
  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current)
      }
    }
  }, [])

  return (
    <span className={className}>
      <span style={{ whiteSpace: 'pre-wrap' }}>{displayedContent}</span>
      {isStreaming && showCursor && (
        <motion.span
          animate={{ opacity: [0, 1, 0] }}
          transition={{ duration: 0.8, repeat: Infinity, ease: "easeInOut" }}
          className="inline-block w-0.5 h-4 bg-blue-400 ml-0.5"
        />
      )}
    </span>
  )
}

export default StreamingText
