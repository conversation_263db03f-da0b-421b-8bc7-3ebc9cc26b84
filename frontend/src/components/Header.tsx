import React from 'react'
import { Layout, But<PERSON>, Space, Typography, Tooltip } from 'antd'
import { motion } from 'framer-motion'
import { 
  Bot, 
  Trash2, 
  Settings, 
  Moon, 
  Sun,
  Sparkles
} from 'lucide-react'

const { Header: AntHeader } = Layout
const { Title } = Typography

interface HeaderProps {
  onClearChat?: () => void
  onToggleTheme?: () => void
  isDark?: boolean
}

const Header: React.FC<HeaderProps> = ({ 
  onClearChat, 
  onToggleTheme, 
  isDark = true 
}) => {
  return (
    <AntHeader className="glass-effect border-b border-slate-600/30 px-6 h-16 flex items-center justify-between sticky top-0 z-50">
      {/* Logo Section */}
      <motion.div
        initial={{ opacity: 0, x: -20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.6 }}
        className="flex items-center space-x-3"
      >
        <motion.div
          animate={{ 
            rotate: [0, 360],
            scale: [1, 1.1, 1]
          }}
          transition={{ 
            duration: 4,
            repeat: Infinity,
            ease: "easeInOut"
          }}
          className="relative"
        >
          <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
            <Bot className="w-6 h-6 text-white" />
          </div>
          <motion.div
            animate={{ 
              scale: [1, 1.2, 1],
              opacity: [0.5, 1, 0.5]
            }}
            transition={{ 
              duration: 2,
              repeat: Infinity,
              ease: "easeInOut"
            }}
            className="absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl blur-md -z-10"
          />
        </motion.div>
        
        <div>
          <Title level={4} className="!text-white !mb-0 gradient-text">
            AutoGen Chat
          </Title>
          <motion.div
            initial={{ width: 0 }}
            animate={{ width: "100%" }}
            transition={{ duration: 1, delay: 0.5 }}
            className="h-0.5 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full"
          />
        </div>
        
        <motion.div
          animate={{ 
            rotate: [0, 10, -10, 0],
            scale: [1, 1.1, 1]
          }}
          transition={{ 
            duration: 3,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        >
          <Sparkles className="w-5 h-5 text-yellow-400" />
        </motion.div>
      </motion.div>

      {/* Action Buttons */}
      <motion.div
        initial={{ opacity: 0, x: 20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.6, delay: 0.2 }}
      >
        <Space size="small">
          <Tooltip title="清空聊天记录">
            <Button
              type="text"
              icon={<Trash2 className="w-4 h-4" />}
              onClick={() => {
                console.log('🗑️ Header: 清空按钮被点击')
                onClearChat?.()
              }}
              className="!text-slate-300 hover:!text-white hover:!bg-slate-700/50 !border-none transition-all duration-300"
            />
          </Tooltip>
          
          <Tooltip title="设置">
            <Button
              type="text"
              icon={<Settings className="w-4 h-4" />}
              className="!text-slate-300 hover:!text-white hover:!bg-slate-700/50 !border-none transition-all duration-300"
            />
          </Tooltip>
          
          <Tooltip title={isDark ? "切换到亮色模式" : "切换到暗色模式"}>
            <Button
              type="text"
              icon={isDark ? <Sun className="w-4 h-4" /> : <Moon className="w-4 h-4" />}
              onClick={onToggleTheme}
              className="!text-slate-300 hover:!text-white hover:!bg-slate-700/50 !border-none transition-all duration-300"
            />
          </Tooltip>
        </Space>
      </motion.div>
    </AntHeader>
  )
}

export default Header
