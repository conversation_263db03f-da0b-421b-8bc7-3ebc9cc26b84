interface ChatMessage {
  message: string
}

interface ChatResponse {
  content: string
  status: string
}

class ChatService {
  private baseUrl: string

  constructor() {
    this.baseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000'
  }

  /**
   * 发送普通消息（非流式）
   */
  async sendMessage(message: string): Promise<string> {
    try {
      const response = await fetch(`${this.baseUrl}/chat`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ message }),
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data: ChatResponse = await response.json()
      return data.content
    } catch (error) {
      console.error('Error sending message:', error)
      throw new Error('发送消息失败，请重试')
    }
  }

  /**
   * 发送流式消息
   */
  async sendStreamMessage(
    message: string,
    onChunk: (chunk: string) => void,
    onComplete: () => void,
    onError: (error: string) => void
  ): Promise<void> {
    console.log('🚀 开始发送流式消息:', message)

    try {
      const response = await fetch(`${this.baseUrl}/chat/stream`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ message }),
      })

      console.log('📡 响应状态:', response.status, response.statusText)

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const reader = response.body?.getReader()
      if (!reader) {
        throw new Error('无法获取响应流')
      }

      const decoder = new TextDecoder()
      let buffer = ''
      let hasReceivedData = false

      try {
        while (true) {
          const { done, value } = await reader.read()

          if (done) {
            console.log('📥 流式读取完成')
            if (!hasReceivedData) {
              onComplete()
            }
            break
          }

          buffer += decoder.decode(value, { stream: true })
          const lines = buffer.split('\n')
          buffer = lines.pop() || '' // 保留不完整的行

          for (const line of lines) {
            if (line.trim() === '') continue // 跳过空行

            console.log('📝 收到原始行:', line)

            if (line.startsWith('data: ')) {
              const data = line.slice(6).trim()
              console.log('🔍 解析数据:', data)

              if (data === '[DONE]') {
                console.log('✅ 收到完成标记')
                onComplete()
                return
              }

              if (data === '') continue // 跳过空数据

              try {
                const parsed = JSON.parse(data)
                console.log('📊 解析结果:', parsed)

                if (parsed.error) {
                  console.error('❌ 服务器错误:', parsed.error)
                  onError(parsed.error)
                  return
                }

                if (parsed.content) {
                  hasReceivedData = true
                  console.log('📝 收到内容块:', parsed.content)
                  onChunk(parsed.content)
                }

                if (parsed.status === 'end') {
                  console.log('✅ 收到结束状态')
                  onComplete()
                  return
                }
              } catch (parseError) {
                console.warn('⚠️ JSON解析失败:', data, parseError)
                // 如果不是JSON，可能是纯文本内容
                if (data && !data.startsWith('{')) {
                  hasReceivedData = true
                  onChunk(data)
                }
              }
            }
          }
        }
      } finally {
        reader.releaseLock()
      }
    } catch (error) {
      console.error('💥 流式请求错误:', error)
      onError(error instanceof Error ? error.message : '流式请求失败')
    }
  }

  /**
   * 重置对话
   */
  async resetConversation(): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/reset`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
    } catch (error) {
      console.error('Error resetting conversation:', error)
      throw new Error('重置对话失败')
    }
  }

  /**
   * 获取可用模型
   */
  async getModels(): Promise<any> {
    try {
      const response = await fetch(`${this.baseUrl}/models`)
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      return await response.json()
    } catch (error) {
      console.error('Error getting models:', error)
      throw new Error('获取模型信息失败')
    }
  }

  /**
   * 健康检查
   */
  async healthCheck(): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/health`)
      return response.ok
    } catch (error) {
      console.error('Health check failed:', error)
      return false
    }
  }
}

// 导出单例实例
export const chatService = new ChatService()
export default ChatService
