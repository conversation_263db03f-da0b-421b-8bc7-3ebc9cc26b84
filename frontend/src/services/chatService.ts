interface ChatMessage {
  message: string
}

interface ChatResponse {
  content: string
  status: string
}

class ChatService {
  private baseUrl: string

  constructor() {
    this.baseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000'
  }

  /**
   * 发送普通消息（非流式）
   */
  async sendMessage(message: string): Promise<string> {
    try {
      const response = await fetch(`${this.baseUrl}/chat`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ message }),
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data: ChatResponse = await response.json()
      return data.content
    } catch (error) {
      console.error('Error sending message:', error)
      throw new Error('发送消息失败，请重试')
    }
  }

  /**
   * 发送流式消息
   */
  async sendStreamMessage(
    message: string,
    onChunk: (chunk: string) => void,
    onComplete: () => void,
    onError: (error: string) => void
  ): Promise<void> {
    console.log('🚀 开始发送流式消息:', message)

    try {
      const response = await fetch(`${this.baseUrl}/chat/stream`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ message }),
      })

      console.log('📡 响应状态:', response.status, response.statusText)

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const reader = response.body?.getReader()
      if (!reader) {
        throw new Error('无法获取响应流')
      }

      const decoder = new TextDecoder()
      let buffer = ''
      let hasReceivedData = false
      let currentEvent = { type: '', id: '', data: '' }

      try {
        while (true) {
          const { done, value } = await reader.read()

          if (done) {
            console.log('📥 SSE流式读取完成')
            if (!hasReceivedData) {
              onComplete()
            }
            break
          }

          buffer += decoder.decode(value, { stream: true })
          const lines = buffer.split('\n')
          buffer = lines.pop() || '' // 保留不完整的行

          for (const line of lines) {
            console.log('📝 收到SSE原始行:', line)

            if (line.trim() === '') {
              // 空行表示一个SSE事件结束，处理累积的事件
              if (currentEvent.data) {
                this.handleSSEEvent(currentEvent, onChunk, onComplete, onError)
                hasReceivedData = true
                // 重置事件
                currentEvent = { type: '', id: '', data: '' }
              }
              continue
            }

            // 解析SSE字段
            if (line.startsWith('event: ')) {
              currentEvent.type = line.slice(7).trim()
              console.log('🎯 SSE事件类型:', currentEvent.type)
            } else if (line.startsWith('id: ')) {
              currentEvent.id = line.slice(4).trim()
              console.log('🆔 SSE事件ID:', currentEvent.id)
            } else if (line.startsWith('data: ')) {
              currentEvent.data = line.slice(6).trim()
              console.log('📊 SSE事件数据:', currentEvent.data)
            }
          }
        }
      } finally {
        reader.releaseLock()
      }
    } catch (error) {
      console.error('💥 流式请求错误:', error)
      onError(error instanceof Error ? error.message : '流式请求失败')
    }
  }

  /**
   * 处理SSE事件
   */
  private handleSSEEvent(
    event: { type: string; id: string; data: string },
    onChunk: (chunk: string) => void,
    onComplete: () => void,
    onError: (error: string) => void
  ): void {
    console.log(`🎯 处理SSE事件: type=${event.type}, id=${event.id}, data=${event.data}`)

    try {
      if (event.data === '[DONE]') {
        console.log('✅ SSE流式响应完成')
        onComplete()
        return
      }

      // 尝试解析JSON数据
      let parsedData: any
      try {
        parsedData = JSON.parse(event.data)
      } catch (parseError) {
        // 如果不是JSON，可能是纯文本内容
        if (event.data && !event.data.startsWith('{')) {
          console.log('📝 收到纯文本内容:', event.data)
          onChunk(event.data)
        }
        return
      }

      console.log('📊 解析后的SSE数据:', parsedData)

      // 根据事件类型处理
      switch (event.type) {
        case 'connected':
          console.log('🔗 SSE连接已建立:', parsedData.message)
          break

        case 'start':
          console.log('🚀 开始处理请求:', parsedData.message)
          break

        case 'chunk':
          if (parsedData.content && parsedData.status === 'streaming') {
            console.log(`📝 接收到内容块 ${parsedData.chunk_id}:`, parsedData.content)
            onChunk(parsedData.content)
          }
          break

        case 'complete':
          console.log(`✅ 处理完成，共 ${parsedData.total_chunks} 个块`)
          break

        case 'end':
          console.log('🏁 SSE事件流结束')
          onComplete()
          break

        case 'error':
          console.error('❌ 服务器返回错误:', parsedData.error)
          onError(parsedData.error)
          break

        default:
          // 处理没有事件类型的情况（兼容旧格式）
          if (parsedData.error) {
            console.error('❌ 服务器错误:', parsedData.error)
            onError(parsedData.error)
          } else if (parsedData.content) {
            console.log('📝 收到内容块:', parsedData.content)
            onChunk(parsedData.content)
          } else if (parsedData.status === 'end') {
            console.log('✅ 收到结束状态')
            onComplete()
          }
          break
      }
    } catch (error) {
      console.warn('⚠️ 处理SSE事件失败:', error, '事件数据:', event.data)
    }
  }

  /**
   * 重置对话
   */
  async resetConversation(): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/reset`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
    } catch (error) {
      console.error('Error resetting conversation:', error)
      throw new Error('重置对话失败')
    }
  }

  /**
   * 获取可用模型
   */
  async getModels(): Promise<any> {
    try {
      const response = await fetch(`${this.baseUrl}/models`)
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      return await response.json()
    } catch (error) {
      console.error('Error getting models:', error)
      throw new Error('获取模型信息失败')
    }
  }

  /**
   * 健康检查
   */
  async healthCheck(): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/health`)
      return response.ok
    } catch (error) {
      console.error('Health check failed:', error)
      return false
    }
  }
}

// 导出单例实例
export const chatService = new ChatService()
export default ChatService
