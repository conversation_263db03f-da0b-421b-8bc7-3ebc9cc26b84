import React, { useRef } from 'react'
import { Layout } from 'antd'
import { motion } from 'framer-motion'
import ChatInterface from './components/ChatInterface'
import Header from './components/Header'
import BackgroundEffects from './components/BackgroundEffects'

const { Content } = Layout

// 定义ChatInterface的方法接口
export interface ChatInterfaceRef {
  clearChat: () => void
}

const App: React.FC = () => {
  const chatInterfaceRef = useRef<ChatInterfaceRef>(null)

  // 处理清空聊天
  const handleClearChat = () => {
    console.log('🗑️ App: 触发清空聊天')
    chatInterfaceRef.current?.clearChat()
  }

  return (
    <Layout className="min-h-screen relative overflow-hidden">
      {/* Background Effects */}
      <BackgroundEffects />

      {/* Header */}
      <Header onClearChat={handleClearChat} />

      {/* Main Content */}
      <Content className="flex-1 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
          className="h-full"
        >
          <ChatInterface ref={chatInterfaceRef} />
        </motion.div>
      </Content>
    </Layout>
  )
}

export default App
