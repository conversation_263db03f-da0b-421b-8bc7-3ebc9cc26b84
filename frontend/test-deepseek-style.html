<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DeepSeek风格流式输出测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
            color: #e2e8f0;
            line-height: 1.6;
            min-height: 100vh;
        }
        .container {
            background: rgba(30, 41, 59, 0.8);
            backdrop-filter: blur(10px);
            padding: 24px;
            border-radius: 16px;
            margin-bottom: 20px;
            border: 1px solid rgba(148, 163, 184, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        h1, h2 {
            color: #60a5fa;
            margin-top: 0;
            font-weight: 600;
        }
        h1 {
            text-align: center;
            font-size: 2rem;
            margin-bottom: 2rem;
            background: linear-gradient(135deg, #60a5fa, #34d399);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        button {
            padding: 12px 24px;
            margin: 8px;
            border: 1px solid #475569;
            border-radius: 8px;
            background: linear-gradient(135deg, #1e293b, #334155);
            color: #e2e8f0;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        }
        button:hover {
            background: linear-gradient(135deg, #334155, #475569);
            border-color: #60a5fa;
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(96, 165, 250, 0.2);
        }
        button:active {
            transform: translateY(0);
        }
        .test-area {
            background: #0f172a;
            padding: 20px;
            border-radius: 12px;
            font-family: 'SF Mono', 'Monaco', 'Consolas', monospace;
            white-space: pre-wrap;
            min-height: 200px;
            border: 1px solid #334155;
            margin-top: 16px;
            position: relative;
            overflow-y: auto;
            max-height: 400px;
        }
        .cursor {
            display: inline-block;
            width: 2px;
            height: 20px;
            background: #60a5fa;
            margin-left: 2px;
            animation: blink 0.8s infinite;
        }
        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
        }
        .input-area {
            display: flex;
            gap: 12px;
            align-items: flex-end;
            margin-top: 16px;
        }
        input[type="text"], textarea {
            flex: 1;
            padding: 12px 16px;
            border: 1px solid #475569;
            border-radius: 8px;
            background: rgba(15, 23, 42, 0.8);
            color: #e2e8f0;
            font-size: 14px;
            font-family: inherit;
            resize: vertical;
            min-height: 44px;
            line-height: 1.5;
        }
        input[type="text"]:focus, textarea:focus {
            outline: none;
            border-color: #60a5fa;
            box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1);
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 12px;
            margin-top: 16px;
        }
        .stat-item {
            background: rgba(15, 23, 42, 0.6);
            padding: 12px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid #334155;
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 600;
            color: #34d399;
        }
        .stat-label {
            font-size: 0.875rem;
            color: #94a3b8;
            margin-top: 4px;
        }
        .demo-text {
            color: #94a3b8;
            font-size: 14px;
            margin-bottom: 16px;
            padding: 12px;
            background: rgba(15, 23, 42, 0.4);
            border-radius: 8px;
            border-left: 4px solid #60a5fa;
        }
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }
        .comparison-item {
            background: rgba(15, 23, 42, 0.6);
            padding: 16px;
            border-radius: 8px;
            border: 1px solid #334155;
        }
        .comparison-title {
            color: #60a5fa;
            font-weight: 600;
            margin-bottom: 12px;
            text-align: center;
        }
    </style>
</head>
<body>
    <h1>🌊 DeepSeek风格流式输出测试</h1>
    
    <div class="container">
        <h2>🎯 优化效果对比</h2>
        <div class="demo-text">
            本测试页面展示了类似DeepSeek官网的流式输出效果，包括：<br>
            ✅ 去掉Markdown格式标记（**粗体**等）<br>
            ✅ 更平滑的打字机效果<br>
            ✅ 智能的停顿节奏<br>
            ✅ 优化的换行显示
        </div>
        
        <div class="input-area">
            <textarea id="testInput" placeholder="输入测试文本...（支持换行）" rows="3">你好！我是一个**智能助手**，可以帮助您解答各种问题。

我的特点包括：
- *快速响应*
- `准确理解`
- **友好交流**

请问有什么可以帮助您的吗？</textarea>
            <button onclick="testDeepSeekStyle()">🚀 测试DeepSeek风格</button>
        </div>
        
        <div class="comparison">
            <div class="comparison-item">
                <div class="comparison-title">❌ 优化前效果</div>
                <div id="beforeOutput" class="test-area"></div>
                <button onclick="testOldStyle()">测试旧版本</button>
            </div>
            
            <div class="comparison-item">
                <div class="comparison-title">✅ DeepSeek风格</div>
                <div id="afterOutput" class="test-area"></div>
                <button onclick="testDeepSeekStyle()">测试新版本</button>
            </div>
        </div>
    </div>

    <div class="container">
        <h2>📊 性能统计</h2>
        <div class="stats">
            <div class="stat-item">
                <div class="stat-value" id="charCount">0</div>
                <div class="stat-label">字符数</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="speed">0</div>
                <div class="stat-label">速度(字符/秒)</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="duration">0</div>
                <div class="stat-label">总时长(秒)</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="cleanedChars">0</div>
                <div class="stat-label">清理后字符</div>
            </div>
        </div>
    </div>

    <div class="container">
        <h2>🧪 实时测试</h2>
        <div class="input-area">
            <input type="text" id="quickTest" placeholder="快速测试..." value="1+1等于多少？">
            <button onclick="testRealAPI()">🔗 测试真实API</button>
            <button onclick="clearAll()">🧹 清空</button>
        </div>
        <div id="realOutput" class="test-area"></div>
    </div>

    <script>
        let isStreaming = false;
        let startTime = 0;

        // 清理Markdown格式
        function cleanMarkdown(text) {
            return text
                .replace(/\*\*(.*?)\*\*/g, '$1')  // 移除粗体
                .replace(/\*(.*?)\*/g, '$1')      // 移除斜体
                .replace(/`(.*?)`/g, '$1')        // 移除行内代码
                .replace(/^#{1,6}\s+/gm, '')      // 移除标题
                .replace(/^\s*[-*+]\s+/gm, '• ')  // 转换列表
                .replace(/^\s*\d+\.\s+/gm, '')    // 移除有序列表
                .trim();
        }

        // DeepSeek风格流式输出
        async function streamText(text, outputElement, options = {}) {
            const {
                speed = 25,
                cleanFormat = true,
                showCursor = true
            } = options;

            if (isStreaming) return;
            isStreaming = true;
            startTime = Date.now();

            const cleanedText = cleanFormat ? cleanMarkdown(text) : text;
            outputElement.innerHTML = '';
            
            let displayedText = '';
            
            for (let i = 0; i < cleanedText.length; i++) {
                if (!isStreaming) break;
                
                const char = cleanedText[i];
                displayedText += char;
                
                outputElement.innerHTML = displayedText + (showCursor ? '<span class="cursor"></span>' : '');
                
                // 根据字符类型调整延迟
                let delay = speed;
                if (char === '。' || char === '！' || char === '？') {
                    delay = speed * 4;  // 句号后长停顿
                } else if (char === '，' || char === '；' || char === '：') {
                    delay = speed * 2;  // 逗号后中停顿
                } else if (char === '\n') {
                    delay = speed * 1.5;  // 换行后短停顿
                } else if (char === ' ') {
                    delay = speed * 0.5;  // 空格快速
                }
                
                await new Promise(resolve => setTimeout(resolve, delay));
                
                // 更新统计
                updateStats(i + 1, cleanedText.length, Date.now() - startTime);
            }
            
            // 移除光标
            outputElement.innerHTML = displayedText;
            isStreaming = false;
        }

        // 旧版本流式输出（对比用）
        async function streamTextOld(text, outputElement) {
            if (isStreaming) return;
            isStreaming = true;
            startTime = Date.now();

            outputElement.innerHTML = '';
            let displayedText = '';
            
            for (let i = 0; i < text.length; i++) {
                if (!isStreaming) break;
                
                displayedText += text[i];
                outputElement.innerHTML = displayedText + '<span class="cursor"></span>';
                
                await new Promise(resolve => setTimeout(resolve, 50));  // 固定延迟
                updateStats(i + 1, text.length, Date.now() - startTime);
            }
            
            outputElement.innerHTML = displayedText;
            isStreaming = false;
        }

        // 更新统计信息
        function updateStats(current, total, elapsed) {
            document.getElementById('charCount').textContent = current;
            document.getElementById('duration').textContent = (elapsed / 1000).toFixed(1);
            document.getElementById('speed').textContent = (current / (elapsed / 1000)).toFixed(1);
            
            const cleaned = cleanMarkdown(document.getElementById('testInput').value);
            document.getElementById('cleanedChars').textContent = cleaned.length;
        }

        // 测试DeepSeek风格
        async function testDeepSeekStyle() {
            const text = document.getElementById('testInput').value;
            const output = document.getElementById('afterOutput');
            await streamText(text, output, { speed: 20, cleanFormat: true });
        }

        // 测试旧版本
        async function testOldStyle() {
            const text = document.getElementById('testInput').value;
            const output = document.getElementById('beforeOutput');
            await streamTextOld(text, output);
        }

        // 测试真实API
        async function testRealAPI() {
            const message = document.getElementById('quickTest').value;
            const output = document.getElementById('realOutput');
            
            if (!message.trim()) return;
            
            output.innerHTML = '🔄 连接到后端API...';
            
            try {
                const response = await fetch('http://localhost:8000/chat/stream', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ message })
                });

                if (!response.ok) throw new Error(`HTTP ${response.status}`);

                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let buffer = '';
                let content = '';

                output.innerHTML = '';
                startTime = Date.now();

                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;

                    buffer += decoder.decode(value, { stream: true });
                    const lines = buffer.split('\n');
                    buffer = lines.pop() || '';

                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            const data = line.slice(6).trim();
                            if (data === '[DONE]') return;

                            try {
                                const parsed = JSON.parse(data);
                                if (parsed.content) {
                                    content += parsed.content;
                                    output.innerHTML = cleanMarkdown(content) + '<span class="cursor"></span>';
                                }
                            } catch (e) {
                                console.warn('解析失败:', data);
                            }
                        }
                    }
                }

                output.innerHTML = cleanMarkdown(content);
            } catch (error) {
                output.innerHTML = `❌ 连接失败: ${error.message}`;
            }
        }

        // 清空所有输出
        function clearAll() {
            isStreaming = false;
            document.getElementById('beforeOutput').innerHTML = '';
            document.getElementById('afterOutput').innerHTML = '';
            document.getElementById('realOutput').innerHTML = '';
            
            // 重置统计
            document.getElementById('charCount').textContent = '0';
            document.getElementById('speed').textContent = '0';
            document.getElementById('duration').textContent = '0';
            document.getElementById('cleanedChars').textContent = '0';
        }

        // 页面加载时的初始化
        window.onload = function() {
            console.log('🚀 DeepSeek风格测试页面已加载');
            
            // 自动计算清理后的字符数
            document.getElementById('testInput').addEventListener('input', function() {
                const cleaned = cleanMarkdown(this.value);
                document.getElementById('cleanedChars').textContent = cleaned.length;
            });
            
            // 初始计算
            const initialText = document.getElementById('testInput').value;
            document.getElementById('cleanedChars').textContent = cleanMarkdown(initialText).length;
        };
    </script>
</body>
</html>
