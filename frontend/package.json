{"name": "autogen-chat-frontend", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@ant-design/x": "^1.0.0", "antd": "^5.12.0", "react": "^18.2.0", "react-dom": "^18.2.0", "framer-motion": "^10.16.0", "lucide-react": "^0.294.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0"}, "devDependencies": {"@types/node": "^20.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.0.3", "autoprefixer": "^10.4.16", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "typescript": "^5.0.2", "vite": "^4.4.5"}}