<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>清空功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .container {
            background: #2a2a2a;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: 1px solid #444;
            border-radius: 5px;
            background: #0066cc;
            color: #fff;
            cursor: pointer;
            font-size: 14px;
        }
        button:hover {
            background: #0052a3;
        }
        button:active {
            background: #003d7a;
            transform: scale(0.98);
        }
        .test-result {
            background: #000;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            margin-top: 10px;
        }
        .success { color: #4ade80; }
        .error { color: #f87171; }
        .info { color: #60a5fa; }
    </style>
</head>
<body>
    <h1>🧪 清空聊天功能测试</h1>
    
    <div class="container">
        <h2>🔧 测试说明</h2>
        <p>这个页面用于测试清空聊天功能的各种场景：</p>
        <ul>
            <li>测试前端清空功能</li>
            <li>测试后端重置接口</li>
            <li>测试按钮点击响应</li>
            <li>测试状态重置</li>
        </ul>
    </div>

    <div class="container">
        <h2>🎯 功能测试</h2>
        <button onclick="testFrontendClear()">🗑️ 测试前端清空</button>
        <button onclick="testBackendReset()">🔄 测试后端重置</button>
        <button onclick="testButtonClick()">👆 测试按钮点击</button>
        <button onclick="testFullFlow()">🚀 测试完整流程</button>
        <button onclick="clearLog()">🧹 清空日志</button>
        
        <div id="testResult" class="test-result"></div>
    </div>

    <div class="container">
        <h2>🔍 问题诊断</h2>
        <button onclick="checkElements()">🔍 检查DOM元素</button>
        <button onclick="checkEventListeners()">👂 检查事件监听</button>
        <button onclick="checkStyles()">🎨 检查样式</button>
        <button onclick="simulateClick()">🖱️ 模拟点击</button>
        
        <div id="diagnosticResult" class="test-result"></div>
    </div>

    <script>
        function log(message, type = 'info') {
            const resultDiv = document.getElementById('testResult');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'success' ? 'success' : type === 'error' ? 'error' : 'info';
            resultDiv.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            resultDiv.scrollTop = resultDiv.scrollHeight;
        }

        function logDiagnostic(message, type = 'info') {
            const resultDiv = document.getElementById('diagnosticResult');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'success' ? 'success' : type === 'error' ? 'error' : 'info';
            resultDiv.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            resultDiv.scrollTop = resultDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('testResult').innerHTML = '';
            document.getElementById('diagnosticResult').innerHTML = '';
        }

        async function testFrontendClear() {
            log('🧪 开始测试前端清空功能...');
            
            // 模拟前端清空逻辑
            try {
                // 模拟重置状态
                const mockState = {
                    messages: [],
                    inputValue: '',
                    isLoading: false,
                    streamingMessageId: null
                };
                
                log('✅ 前端状态重置成功', 'success');
                log(`📊 重置后状态: ${JSON.stringify(mockState)}`);
                
                // 模拟成功消息
                log('💬 显示成功消息: "聊天记录已清空"', 'success');
                
            } catch (error) {
                log(`❌ 前端清空失败: ${error.message}`, 'error');
            }
        }

        async function testBackendReset() {
            log('🌐 开始测试后端重置接口...');
            
            try {
                const response = await fetch('http://localhost:8000/reset', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                });

                if (response.ok) {
                    const data = await response.json();
                    log('✅ 后端重置成功', 'success');
                    log(`📊 响应数据: ${JSON.stringify(data)}`);
                } else {
                    log(`❌ 后端重置失败: HTTP ${response.status}`, 'error');
                }
            } catch (error) {
                log(`❌ 后端重置异常: ${error.message}`, 'error');
            }
        }

        function testButtonClick() {
            log('👆 测试按钮点击响应...');
            
            // 创建测试按钮
            const testButton = document.createElement('button');
            testButton.textContent = '🗑️ 测试清空';
            testButton.onclick = function() {
                log('✅ 按钮点击事件触发', 'success');
                log('🔄 执行清空逻辑...');
                
                // 模拟清空逻辑
                setTimeout(() => {
                    log('✅ 清空逻辑执行完成', 'success');
                }, 100);
            };
            
            // 模拟点击
            testButton.click();
            log('📝 测试按钮创建并点击完成');
        }

        async function testFullFlow() {
            log('🚀 开始测试完整清空流程...');
            
            // 1. 测试前端清空
            await testFrontendClear();
            
            // 2. 等待一秒
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // 3. 测试后端重置
            await testBackendReset();
            
            // 4. 等待一秒
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // 5. 测试按钮点击
            testButtonClick();
            
            log('🎉 完整流程测试完成', 'success');
        }

        function checkElements() {
            logDiagnostic('🔍 检查DOM元素...');
            
            // 检查是否在React应用中
            if (window.React) {
                logDiagnostic('✅ React已加载', 'success');
            } else {
                logDiagnostic('❌ React未加载', 'error');
            }
            
            // 检查清空按钮
            const clearButtons = document.querySelectorAll('button');
            logDiagnostic(`📊 找到 ${clearButtons.length} 个按钮元素`);
            
            clearButtons.forEach((btn, index) => {
                const text = btn.textContent || btn.innerText;
                if (text.includes('清空') || text.includes('clear')) {
                    logDiagnostic(`✅ 找到清空按钮 ${index + 1}: "${text}"`, 'success');
                }
            });
        }

        function checkEventListeners() {
            logDiagnostic('👂 检查事件监听器...');
            
            // 这个在实际应用中比较难检测，但可以提供指导
            logDiagnostic('💡 检查要点:');
            logDiagnostic('  - onClick事件是否正确绑定');
            logDiagnostic('  - 事件处理函数是否存在');
            logDiagnostic('  - 是否有事件冒泡问题');
            logDiagnostic('  - 按钮是否被禁用');
        }

        function checkStyles() {
            logDiagnostic('🎨 检查样式问题...');
            
            logDiagnostic('💡 常见样式问题:');
            logDiagnostic('  - pointer-events: none (阻止点击)');
            logDiagnostic('  - z-index 层级问题');
            logDiagnostic('  - position 定位覆盖');
            logDiagnostic('  - opacity: 0 (不可见)');
            logDiagnostic('  - display: none (隐藏)');
        }

        function simulateClick() {
            logDiagnostic('🖱️ 模拟点击测试...');
            
            // 创建点击事件
            const clickEvent = new MouseEvent('click', {
                bubbles: true,
                cancelable: true,
                view: window
            });
            
            logDiagnostic('✅ 点击事件创建成功', 'success');
            logDiagnostic('💡 在实际应用中，可以用此方法测试按钮响应');
        }

        // 页面加载时的初始化
        window.onload = function() {
            log('🚀 清空功能测试工具已加载', 'success');
            log('💡 请按顺序执行测试，或直接运行完整流程测试');
        };
    </script>
</body>
</html>
