from fastapi import Fast<PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
import json
import asyncio
from typing import AsyncGenerator
import uvicorn

from autogen_service import AutoGenService

app = FastAPI(
    title="AutoGen Chat API",
    description="基于AutoGen的聊天API，支持SSE流式输出",
    version="1.0.0"
)

# CORS配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境中应该设置具体的域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 初始化AutoGen服务
autogen_service = AutoGenService()

class ChatMessage(BaseModel):
    message: str

class ChatResponse(BaseModel):
    content: str
    status: str = "success"

@app.get("/")
async def root():
    """根路径，返回API信息"""
    return {
        "message": "AutoGen Chat API",
        "version": "1.0.0",
        "docs": "/docs",
        "status": "running"
    }

@app.get("/health")
async def health_check():
    """健康检查接口"""
    return {"status": "healthy", "service": "autogen-chat"}

@app.post("/chat")
async def chat(message: ChatMessage) -> ChatResponse:
    """
    普通聊天接口（非流式）
    """
    try:
        if not message.message.strip():
            raise HTTPException(status_code=400, detail="消息内容不能为空")
        
        response = await autogen_service.get_response(message.message)
        return ChatResponse(content=response)
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"处理消息时发生错误: {str(e)}")

@app.post("/chat/stream")
async def chat_stream(message: ChatMessage):
    """
    流式聊天接口（SSE）
    """
    try:
        if not message.message.strip():
            raise HTTPException(status_code=400, detail="消息内容不能为空")
        
        async def generate_sse_response() -> AsyncGenerator[str, None]:
            """
            生成符合SSE协议标准的响应流
            SSE格式规范：
            event: <事件类型>
            data: <JSON数据>
            id: <消息ID>

            """
            try:
                print(f"🚀 开始SSE流式响应: {message.message}")
                message_id = 0

                # SSE连接建立事件
                message_id += 1
                yield f"event: connected\n"
                yield f"id: {message_id}\n"
                yield f"data: {json.dumps({'status': 'connected', 'message': 'SSE连接已建立'}, ensure_ascii=False)}\n\n"

                # 开始处理事件
                message_id += 1
                yield f"event: start\n"
                yield f"id: {message_id}\n"
                yield f"data: {json.dumps({'status': 'start', 'message': '开始处理您的请求'}, ensure_ascii=False)}\n\n"

                # 获取流式响应 - DeepSeek风格优化
                chunk_count = 0
                full_content = ""
                buffer = ""

                async for chunk in autogen_service.get_stream_response(message.message):
                    if chunk:
                        buffer += chunk
                        full_content += chunk

                        # DeepSeek风格：按词组或短语发送，提供更流畅的阅读体验
                        should_send = False

                        # 判断是否应该发送当前缓冲区
                        if len(buffer) >= 2:  # 至少2个字符
                            should_send = True
                        elif chunk in '。！？，；：\n':  # 标点符号立即发送
                            should_send = True
                        elif chunk == ' ' and len(buffer) >= 1:  # 英文单词边界
                            should_send = True

                        if should_send:
                            chunk_count += 1
                            message_id += 1
                            print(f"📝 DeepSeek风格发送chunk {chunk_count}: {repr(buffer)}")

                            # 发送内容块事件（标准SSE格式）
                            yield f"event: chunk\n"
                            yield f"id: {message_id}\n"
                            yield f"data: {json.dumps({'content': buffer, 'status': 'streaming', 'chunk_id': chunk_count}, ensure_ascii=False)}\n\n"

                            buffer = ""  # 清空缓冲区

                            # 根据内容类型调整延迟，模拟真实的思考节奏
                            if chunk in '。！？':
                                await asyncio.sleep(0.15)  # 句号后较长停顿
                            elif chunk in '，；：':
                                await asyncio.sleep(0.08)  # 逗号后中等停顿
                            elif chunk == '\n':
                                await asyncio.sleep(0.05)  # 换行后短停顿
                            else:
                                await asyncio.sleep(0.03)  # 正常流速

                # 发送剩余的缓冲内容
                if buffer:
                    chunk_count += 1
                    message_id += 1
                    print(f"📝 发送最后的chunk {chunk_count}: {repr(buffer)}")

                    yield f"event: chunk\n"
                    yield f"id: {message_id}\n"
                    yield f"data: {json.dumps({'content': buffer, 'status': 'streaming', 'chunk_id': chunk_count}, ensure_ascii=False)}\n\n"

                print(f"✅ SSE流式响应完成，共发送 {chunk_count} 个chunks")

                # 发送完成事件
                message_id += 1
                yield f"event: complete\n"
                yield f"id: {message_id}\n"
                yield f"data: {json.dumps({'status': 'complete', 'total_chunks': chunk_count, 'full_content': full_content}, ensure_ascii=False)}\n\n"

                # 发送结束标记（标准SSE结束）
                message_id += 1
                yield f"event: end\n"
                yield f"id: {message_id}\n"
                yield f"data: [DONE]\n\n"

            except Exception as e:
                print(f"❌ SSE生成响应时出错: {e}")

                # 发送错误事件
                message_id += 1
                yield f"event: error\n"
                yield f"id: {message_id}\n"
                yield f"data: {json.dumps({'error': str(e), 'status': 'error'}, ensure_ascii=False)}\n\n"

                # 发送结束标记
                message_id += 1
                yield f"event: end\n"
                yield f"id: {message_id}\n"
                yield f"data: [DONE]\n\n"
        
        return StreamingResponse(
            generate_sse_response(),
            media_type="text/event-stream",  # 标准SSE媒体类型
            headers={
                # SSE标准头部
                "Cache-Control": "no-cache, no-store, must-revalidate",
                "Connection": "keep-alive",
                "Content-Encoding": "identity",  # 禁用压缩以确保实时传输

                # CORS头部
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
                "Access-Control-Allow-Headers": "Content-Type, Accept, Cache-Control",
                "Access-Control-Expose-Headers": "Content-Type",

                # 服务器优化
                "X-Accel-Buffering": "no",  # 禁用Nginx缓冲
                "X-Content-Type-Options": "nosniff",
            }
        )
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"启动流式响应时发生错误: {str(e)}")

@app.get("/models")
async def get_models():
    """获取可用的模型信息"""
    return {
        "models": [
            {
                "id": "deepseek-chat",
                "name": "DeepSeek Chat",
                "description": "DeepSeek聊天模型",
                "provider": "DeepSeek"
            }
        ],
        "current_model": "deepseek-chat"
    }

@app.post("/reset")
async def reset_conversation():
    """重置对话历史"""
    try:
        await autogen_service.reset_conversation()
        return {"status": "success", "message": "对话历史已重置"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"重置对话时发生错误: {str(e)}")

if __name__ == "__main__":
    print("🚀 启动AutoGen Chat API服务...")
    print("📖 API文档: http://localhost:8000/docs")
    print("🌐 前端地址: 请在浏览器中打开 frontend/index.html")
    
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
