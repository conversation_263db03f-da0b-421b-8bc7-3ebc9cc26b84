from fastapi import Fast<PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
import json
import asyncio
from typing import AsyncGenerator
import uvicorn

from autogen_service import AutoGenService

app = FastAPI(
    title="AutoGen Chat API",
    description="基于AutoGen的聊天API，支持SSE流式输出",
    version="1.0.0"
)

# CORS配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境中应该设置具体的域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 初始化AutoGen服务
autogen_service = AutoGenService()

class ChatMessage(BaseModel):
    message: str

class ChatResponse(BaseModel):
    content: str
    status: str = "success"

@app.get("/")
async def root():
    """根路径，返回API信息"""
    return {
        "message": "AutoGen Chat API",
        "version": "1.0.0",
        "docs": "/docs",
        "status": "running"
    }

@app.get("/health")
async def health_check():
    """健康检查接口"""
    return {"status": "healthy", "service": "autogen-chat"}

@app.post("/chat")
async def chat(message: ChatMessage) -> ChatResponse:
    """
    普通聊天接口（非流式）
    """
    try:
        if not message.message.strip():
            raise HTTPException(status_code=400, detail="消息内容不能为空")
        
        response = await autogen_service.get_response(message.message)
        return ChatResponse(content=response)
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"处理消息时发生错误: {str(e)}")

@app.post("/chat/stream")
async def chat_stream(message: ChatMessage):
    """
    流式聊天接口（SSE）
    """
    try:
        if not message.message.strip():
            raise HTTPException(status_code=400, detail="消息内容不能为空")
        
        async def generate_response() -> AsyncGenerator[str, None]:
            try:
                print(f"🚀 开始生成响应: {message.message}")

                # 发送开始标记
                start_data = {"status": "start"}
                yield f"data: {json.dumps(start_data, ensure_ascii=False)}\n\n"

                # 获取流式响应
                chunk_count = 0
                async for chunk in autogen_service.get_stream_response(message.message):
                    if chunk:
                        chunk_count += 1
                        print(f"📝 发送chunk {chunk_count}: {repr(chunk)}")

                        data = {
                            "content": chunk,
                            "status": "streaming"
                        }
                        yield f"data: {json.dumps(data, ensure_ascii=False)}\n\n"

                        # 添加小延迟以确保流式效果
                        await asyncio.sleep(0.02)

                print(f"✅ 流式响应完成，共发送 {chunk_count} 个chunks")

                # 发送结束标记
                end_data = {"status": "end"}
                yield f"data: {json.dumps(end_data, ensure_ascii=False)}\n\n"
                yield "data: [DONE]\n\n"

            except Exception as e:
                print(f"❌ 生成响应时出错: {e}")
                error_data = {
                    "error": str(e),
                    "status": "error"
                }
                yield f"data: {json.dumps(error_data, ensure_ascii=False)}\n\n"
                yield "data: [DONE]\n\n"
        
        return StreamingResponse(
            generate_response(),
            media_type="text/plain",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Content-Type": "text/event-stream",
            }
        )
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"启动流式响应时发生错误: {str(e)}")

@app.get("/models")
async def get_models():
    """获取可用的模型信息"""
    return {
        "models": [
            {
                "id": "deepseek-chat",
                "name": "DeepSeek Chat",
                "description": "DeepSeek聊天模型",
                "provider": "DeepSeek"
            }
        ],
        "current_model": "deepseek-chat"
    }

@app.post("/reset")
async def reset_conversation():
    """重置对话历史"""
    try:
        await autogen_service.reset_conversation()
        return {"status": "success", "message": "对话历史已重置"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"重置对话时发生错误: {str(e)}")

if __name__ == "__main__":
    print("🚀 启动AutoGen Chat API服务...")
    print("📖 API文档: http://localhost:8000/docs")
    print("🌐 前端地址: 请在浏览器中打开 frontend/index.html")
    
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
