import asyncio
import os
import sys
from typing import AsyncGenerator, List, Dict, Any
from dotenv import load_dotenv

# 添加examples目录到Python路径，以便导入llms模块
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'examples'))

from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.ui import Console
from llms import get_model_client

# 加载环境变量
load_dotenv()

class AutoGenService:
    """
    AutoGen服务类，封装与AutoGen的交互逻辑
    """
    
    def __init__(self):
        """初始化AutoGen服务"""
        self.model_client = None
        self.agent = None
        self.conversation_history: List[Dict[str, Any]] = []
        self._initialize_agent()
    
    def _initialize_agent(self):
        """初始化AutoGen代理"""
        try:
            # 获取模型客户端
            self.model_client = get_model_client()
            
            # 创建助手代理
            self.agent = AssistantAgent(
                name="chat_assistant",
                model_client=self.model_client,
                system_message="""你是一个智能、友好、有帮助的AI助手。你的特点：

1. 🎯 专业能力：
   - 能够回答各种问题，包括技术、学术、生活等领域
   - 擅长编程、数据分析、创意写作等任务
   - 提供准确、详细、有用的信息

2. 💬 交流风格：
   - 友好、耐心、有礼貌
   - 根据用户需求调整回答的详细程度
   - 使用清晰、易懂的语言

3. 🚀 特殊技能：
   - 代码编写和调试
   - 创意内容生成
   - 问题分析和解决
   - 学习指导和解释

请始终保持积极、有帮助的态度，为用户提供最佳的服务体验。""",
                model_client_stream=True,
            )
            
            print("✅ AutoGen代理初始化成功")
            
        except Exception as e:
            print(f"❌ AutoGen代理初始化失败: {e}")
            raise e
    
    async def get_response(self, message: str) -> str:
        """
        获取非流式响应
        
        Args:
            message: 用户消息
            
        Returns:
            AI回复内容
        """
        try:
            # 记录用户消息
            self.conversation_history.append({
                "role": "user",
                "content": message,
                "timestamp": asyncio.get_event_loop().time()
            })
            
            # 获取AI响应
            result = await self.agent.run(task=message)
            
            # 提取响应内容
            response_content = ""
            if hasattr(result, 'messages') and result.messages:
                # 获取最后一条消息的内容
                last_message = result.messages[-1]
                if hasattr(last_message, 'content'):
                    response_content = last_message.content
                else:
                    response_content = str(last_message)
            else:
                response_content = str(result)
            
            # 记录AI响应
            self.conversation_history.append({
                "role": "assistant", 
                "content": response_content,
                "timestamp": asyncio.get_event_loop().time()
            })
            
            return response_content
            
        except Exception as e:
            error_msg = f"获取响应时发生错误: {str(e)}"
            print(f"❌ {error_msg}")
            return f"抱歉，我遇到了一些技术问题：{str(e)}"
    
    async def get_stream_response(self, message: str) -> AsyncGenerator[str, None]:
        """
        获取流式响应

        Args:
            message: 用户消息

        Yields:
            AI回复的文本片段
        """
        try:
            print(f"🤖 AutoGen开始处理消息: {message}")

            # 记录用户消息
            self.conversation_history.append({
                "role": "user",
                "content": message,
                "timestamp": asyncio.get_event_loop().time()
            })

            # 首先尝试非流式获取完整响应，然后模拟流式输出
            try:
                print("📡 调用AutoGen agent...")
                result = await self.agent.run(task=message)
                print(f"📥 AutoGen响应类型: {type(result)}")

                # 提取响应内容
                response_content = ""
                if hasattr(result, 'messages') and result.messages:
                    # 获取最后一条消息的内容
                    last_message = result.messages[-1]
                    if hasattr(last_message, 'content'):
                        response_content = str(last_message.content)
                    else:
                        response_content = str(last_message)
                else:
                    response_content = str(result)

                print(f"✅ 提取到响应内容: {response_content[:100]}...")

                # 清理Markdown格式
                cleaned_content = self._clean_markdown(response_content)

                # DeepSeek风格流式输出
                if cleaned_content:
                    # 按字符流式输出，但速度更快
                    for i, char in enumerate(cleaned_content):
                        yield char
                        # 根据字符类型调整延迟
                        if char in '。！？':
                            await asyncio.sleep(0.08)  # 句号后稍长停顿
                        elif char in '，；：':
                            await asyncio.sleep(0.04)  # 逗号后短停顿
                        elif char == '\n':
                            await asyncio.sleep(0.02)  # 换行后很短停顿
                        elif i % 3 == 0:  # 每3个字符稍微停顿
                            await asyncio.sleep(0.02)
                        else:
                            await asyncio.sleep(0.01)  # 快速流式

                    # 记录完整的AI响应
                    self.conversation_history.append({
                        "role": "assistant",
                        "content": response_content,
                        "timestamp": asyncio.get_event_loop().time()
                    })
                else:
                    # 如果没有内容，返回默认响应
                    default_response = "我收到了你的消息，但暂时无法生成回复。"
                    for char in default_response:
                        yield char
                        await asyncio.sleep(0.05)

            except Exception as agent_error:
                print(f"⚠️ AutoGen调用失败，尝试流式方式: {agent_error}")

                # 如果普通方式失败，尝试流式方式
                try:
                    stream = self.agent.run_stream(task=message)
                    full_response = ""
                    last_content_length = 0

                    async for chunk in stream:
                        try:
                            if chunk and hasattr(chunk, 'messages') and chunk.messages:
                                # 获取最新消息的内容
                                latest_message = chunk.messages[-1]
                                if hasattr(latest_message, 'content') and latest_message.content:
                                    content = str(latest_message.content)

                                    # 计算新增的内容
                                    if len(content) > last_content_length:
                                        new_content = content[last_content_length:]
                                        last_content_length = len(content)
                                        full_response = content

                                        # 逐字符输出
                                        for char in new_content:
                                            yield char
                                            await asyncio.sleep(0.05)

                        except Exception as chunk_error:
                            print(f"⚠️ 处理chunk时出错: {chunk_error}")
                            continue

                    # 记录完整响应
                    if full_response:
                        self.conversation_history.append({
                            "role": "assistant",
                            "content": full_response,
                            "timestamp": asyncio.get_event_loop().time()
                        })

                except Exception as stream_error:
                    print(f"❌ 流式调用也失败: {stream_error}")
                    # 返回错误信息
                    error_response = f"抱歉，我遇到了技术问题。错误信息：{str(stream_error)}"
                    for char in error_response:
                        yield char
                        await asyncio.sleep(0.05)

        except Exception as e:
            error_msg = f"流式响应时发生错误: {str(e)}"
            print(f"❌ {error_msg}")

            # 返回错误信息
            error_response = f"抱歉，我遇到了一些技术问题：{str(e)}"
            for char in error_response:
                yield char
                await asyncio.sleep(0.05)
    
    async def reset_conversation(self):
        """重置对话历史"""
        try:
            self.conversation_history.clear()
            print("✅ 对话历史已重置")
        except Exception as e:
            print(f"❌ 重置对话历史失败: {e}")
            raise e
    
    def get_conversation_history(self) -> List[Dict[str, Any]]:
        """获取对话历史"""
        return self.conversation_history.copy()

    def _clean_markdown(self, text: str) -> str:
        """
        清理Markdown格式，去掉**等标记，提供更清洁的输出

        Args:
            text: 原始文本

        Returns:
            清理后的文本
        """
        import re

        # 去掉粗体标记 **text**
        text = re.sub(r'\*\*(.*?)\*\*', r'\1', text)

        # 去掉斜体标记 *text*
        text = re.sub(r'\*(.*?)\*', r'\1', text)

        # 去掉行内代码标记 `code`
        text = re.sub(r'`(.*?)`', r'\1', text)

        # 去掉标题标记 # ## ###
        text = re.sub(r'^#{1,6}\s+', '', text, flags=re.MULTILINE)

        # 将列表标记转换为简单的圆点
        text = re.sub(r'^\s*[-*+]\s+', '• ', text, flags=re.MULTILINE)

        # 去掉有序列表数字
        text = re.sub(r'^\s*\d+\.\s+', '', text, flags=re.MULTILINE)

        # 去掉链接格式 [text](url)
        text = re.sub(r'\[([^\]]+)\]\([^)]+\)', r'\1', text)

        # 去掉代码块标记
        text = re.sub(r'```[\s\S]*?```', '', text)
        text = re.sub(r'`([^`]+)`', r'\1', text)

        return text.strip()

    def get_stats(self) -> Dict[str, Any]:
        """获取服务统计信息"""
        return {
            "total_messages": len(self.conversation_history),
            "user_messages": len([msg for msg in self.conversation_history if msg["role"] == "user"]),
            "assistant_messages": len([msg for msg in self.conversation_history if msg["role"] == "assistant"]),
            "model_info": {
                "model_name": os.getenv("MODEL", "deepseek-chat"),
                "base_url": os.getenv("BASE_URL", "https://api.deepseek.com/v1"),
            }
        }

# 测试函数
async def test_autogen_service():
    """测试AutoGen服务"""
    print("🧪 开始测试AutoGen服务...")
    
    service = AutoGenService()
    
    # 测试普通响应
    print("\n📝 测试普通响应:")
    response = await service.get_response("你好，请简单介绍一下你自己")
    print(f"回复: {response}")
    
    # 测试流式响应
    print("\n🌊 测试流式响应:")
    print("回复: ", end="", flush=True)
    async for chunk in service.get_stream_response("写一首关于春天的短诗"):
        print(chunk, end="", flush=True)
    print("\n")
    
    # 显示统计信息
    stats = service.get_stats()
    print(f"\n📊 统计信息: {stats}")

if __name__ == "__main__":
    # 运行测试
    asyncio.run(test_autogen_service())
