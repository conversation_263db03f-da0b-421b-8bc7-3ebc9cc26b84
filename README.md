# AutoGen Chat - 炫酷AI聊天应用

基于AutoGen 0.5.7的现代化AI聊天应用，具有炫酷的UI界面和流式对话体验。

## ✨ 特性

### 🎨 前端特性
- **现代化UI设计**: 参考Gemini风格的炫酷界面
- **React + TypeScript**: 类型安全的现代前端框架
- **Ant Design X**: 使用最新的流式组件
- **Framer Motion**: 丰富的动画效果
- **Tailwind CSS**: 响应式设计和自定义样式
- **实时流式显示**: 打字机效果的消息展示

### 🚀 后端特性
- **FastAPI**: 高性能的Python Web框架
- **SSE流式输出**: Server-Sent Events实现实时通信
- **AutoGen集成**: 基于AutoGen 0.5.7的AI对话
- **异步处理**: 高并发的消息处理能力
- **错误处理**: 完善的异常处理机制

## 🏗️ 项目结构

```
├── frontend/                 # React前端应用
│   ├── src/
│   │   ├── components/      # React组件
│   │   ├── services/        # API服务
│   │   └── main.tsx         # 应用入口
│   ├── package.json
│   └── vite.config.ts
├── backend/                  # FastAPI后端
│   ├── main.py              # FastAPI应用
│   ├── autogen_service.py   # AutoGen服务
│   └── requirements.txt
├── examples/                 # AutoGen示例代码
│   ├── llms.py              # 模型配置
│   └── .env                 # 环境变量
├── start_backend.bat        # 后端启动脚本
├── start_frontend.bat       # 前端启动脚本
└── README.md
```

## 🚀 快速开始

### 前置要求

- Python 3.8+
- Node.js 16+
- npm 或 yarn

### 1. 启动后端服务

```bash
# 方法1: 使用启动脚本 (Windows)
start_backend.bat

# 方法2: 手动启动
cd backend
pip install -r requirements.txt
python main.py
```

后端服务将在 `http://localhost:8000` 启动

### 2. 启动前端应用

```bash
# 方法1: 使用启动脚本 (Windows)
start_frontend.bat

# 方法2: 手动启动
cd frontend
npm install
npm run dev
```

前端应用将在 `http://localhost:3000` 启动

## 🔧 配置

### 后端配置

编辑 `examples/.env` 文件：

```env
MODEL=deepseek-chat
API_KEY=your_api_key_here
BASE_URL=https://api.deepseek.com/v1
```

### 前端配置

编辑 `frontend/.env` 文件：

```env
VITE_API_BASE_URL=http://localhost:8000
VITE_APP_TITLE=AutoGen Chat
```

## 📚 API文档

启动后端服务后，访问以下地址查看API文档：

- Swagger UI: `http://localhost:8000/docs`
- ReDoc: `http://localhost:8000/redoc`

### 主要API端点

- `GET /` - 根路径信息
- `GET /health` - 健康检查
- `POST /chat` - 普通聊天接口
- `POST /chat/stream` - 流式聊天接口
- `POST /reset` - 重置对话历史
- `GET /models` - 获取模型信息

## 🎯 使用说明

1. **启动服务**: 先启动后端，再启动前端
2. **开始聊天**: 在输入框中输入消息，按Enter发送
3. **流式体验**: AI回复将以打字机效果实时显示
4. **清空聊天**: 点击清空按钮重置对话历史

## 🛠️ 开发

### 前端开发

```bash
cd frontend
npm run dev      # 开发模式
npm run build    # 构建生产版本
npm run preview  # 预览构建结果
```

### 后端开发

```bash
cd backend
python main.py   # 启动开发服务器
```

## 🎨 界面预览

- **炫酷背景**: 动态渐变球体和粒子效果
- **流式对话**: 实时打字机效果
- **响应式设计**: 适配各种屏幕尺寸
- **暗色主题**: 现代化的暗色界面

## 🔍 故障排除

### 常见问题

1. **后端启动失败**
   - 检查Python版本和依赖安装
   - 确认AutoGen已正确安装
   - 检查环境变量配置

2. **前端连接失败**
   - 确认后端服务已启动
   - 检查API地址配置
   - 查看浏览器控制台错误

3. **流式响应异常**
   - 检查网络连接
   - 确认API密钥有效
   - 查看后端日志

4. **清空聊天记录无响应**
   - 检查浏览器控制台是否有错误
   - 确认按钮点击事件正确绑定
   - 使用 `frontend/test-clear.html` 测试清空功能
   - 检查网络请求是否成功发送到后端

### 调试工具

- **后端测试**: `python test_backend.py`
- **前端调试**: 打开 `frontend/debug.html`
- **清空功能测试**: 打开 `frontend/test-clear.html`
- **SSE协议测试**: 打开 `frontend/test-sse.html`
- **DeepSeek风格测试**: 打开 `frontend/test-deepseek-style.html`

### SSE协议说明

本项目使用标准的SSE (Server-Sent Events) 协议进行实时流式通信：

#### SSE事件类型
- `connected`: SSE连接建立
- `start`: 开始处理请求
- `chunk`: 内容数据块
- `complete`: 处理完成
- `end`: 流结束
- `error`: 错误事件

#### SSE数据格式
```
event: chunk
id: 123
data: {"content": "Hello", "status": "streaming"}

```

#### 特性
- ✅ 标准SSE协议兼容
- ✅ 自动重连机制
- ✅ 事件类型分类
- ✅ 消息ID追踪
- ✅ 错误处理

### DeepSeek风格优化

本项目已优化为类似DeepSeek官网的流式输出效果：

#### 🎯 核心优化
- **智能流式输出**: 类似DeepSeek的平滑打字机效果
- **Markdown清理**: 自动去掉`**`、`*`、`` ` ``等格式标记
- **换行优化**: 提问时支持多行输入，显示时保持格式
- **智能停顿**: 根据标点符号调整流式速度

#### 🔧 技术实现
- 前端：优化StreamingText组件，实现变速流式显示
- 后端：DeepSeek风格的分块发送策略
- 格式：自动清理Markdown，提供纯净的阅读体验

#### 📊 效果对比
- **优化前**: 固定速度，保留Markdown格式
- **优化后**: 智能变速，清洁格式，更好的阅读体验

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交Issue和Pull Request！

## 📞 支持

如有问题，请查看：
- API文档: `http://localhost:8000/docs`
- AutoGen官方文档: https://microsoft.github.io/autogen/
- 项目Issues页面
